import streamlit as st
import speech_recognition as sr
from io import BytesIO
import numpy as np
import time
import soundfile as sf
import tempfile
import os

def get_voice_input():
    """
    Capture voice input from the user's microphone and convert it to text.
    
    Returns:
        str: The transcribed text from the user's speech
    """
    # Create a placeholder for the recording status
    status_placeholder = st.empty()
    
    # Create a button to start recording
    if 'recording' not in st.session_state:
        st.session_state.recording = False
        st.session_state.voice_query = ""
    
    # Button for recording control
    col1, col2 = st.columns([3, 1])
    with col1:
        if not st.session_state.recording:
            button_text = "🎤 Start Recording"
            button_help = "Click to start recording your voice query"
        else:
            button_text = "⏹️ Stop Recording"
            button_help = "Click to stop recording and transcribe"
    
    with col2:
        record_button = st.button(button_text, help=button_help, use_container_width=True)
    
    # Handle recording button press
    if record_button:
        st.session_state.recording = not st.session_state.recording
        
        if st.session_state.recording:
            # Start recording
            status_placeholder.markdown("""
            <div style="padding: 10px 15px; background-color: #ff4b4b; color: white; border-radius: 5px; 
                       animation: pulse 1.5s infinite; text-align: center; font-weight: bold;">
                🔴 Recording... Speak now!
            </div>
            <style>
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.7; }
                100% { opacity: 1; }
            }
            </style>
            """, unsafe_allow_html=True)
            
            # Initialize the recognizer
            r = sr.Recognizer()
            
            try:
                # Use the default microphone as the audio source
                with sr.Microphone() as source:
                    st.session_state.recording_start_time = time.time()
                    r.adjust_for_ambient_noise(source)
                    
                    # Record for up to 10 seconds
                    audio_data = r.listen(source, timeout=10, phrase_time_limit=10)
                    st.session_state.audio_data = audio_data
                
                # Stop recording state immediately after obtaining audio
                st.session_state.recording = False
                st.rerun()
                
            except sr.RequestError:
                status_placeholder.error("Could not request results from speech recognition service.")
                st.session_state.recording = False
            except sr.WaitTimeoutError:
                status_placeholder.warning("No speech detected within timeout period.")
                st.session_state.recording = False
            except Exception as e:
                status_placeholder.error(f"Error during recording: {str(e)}")
                st.session_state.recording = False
        
        else:
            # Stop recording logic will be handled above or on the next rerun
            pass
    
    # Process audio data if it exists
    if not st.session_state.recording and 'audio_data' in st.session_state:
        status_placeholder.markdown("""
        <div style="padding: 10px 15px; background-color: #4CAF50; color: white; border-radius: 5px; text-align: center; font-weight: bold;">
            ✅ Processing audio...
        </div>
        """, unsafe_allow_html=True)
        
        try:
            # Initialize the recognizer
            r = sr.Recognizer()
            
            # Use Google's speech recognition
            text = r.recognize_google(st.session_state.audio_data)
            
            # Clear the audio data from session state
            del st.session_state.audio_data
            
            # Store the transcribed text
            st.session_state.voice_query = text
            
            # Display success message with the transcribed text
            status_placeholder.markdown(f"""
            <div style="padding: 10px 15px; background-color: #4CAF50; color: white; border-radius: 5px;">
                ✅ Transcribed: "{text}"
            </div>
            """, unsafe_allow_html=True)
            
            return text
            
        except sr.UnknownValueError:
            status_placeholder.error("Could not understand audio")
            del st.session_state.audio_data
        except sr.RequestError as e:
            status_placeholder.error(f"Could not request results from speech recognition service; {e}")
            del st.session_state.audio_data
        except Exception as e:
            status_placeholder.error(f"Error processing audio: {str(e)}")
            if 'audio_data' in st.session_state:
                del st.session_state.audio_data
    
    # If still recording, show the recording status
    if st.session_state.recording:
        elapsed_time = time.time() - st.session_state.recording_start_time
        status_placeholder.markdown(f"""
        <div style="padding: 10px 15px; background-color: #ff4b4b; color: white; border-radius: 5px; 
                  animation: pulse 1.5s infinite; text-align: center; font-weight: bold;">
            🔴 Recording... {elapsed_time:5.1f}s (max 20s)
        </div>
        <style>
        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.7; }}
            100% {{ opacity: 1; }}
        }}
        </style>
        """, unsafe_allow_html=True)
    
    # Return any stored voice query
    if 'voice_query' in st.session_state:
        return st.session_state.voice_query
    
    return None