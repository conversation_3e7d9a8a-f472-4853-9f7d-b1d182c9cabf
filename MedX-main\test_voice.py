import streamlit as st
from voice_input import get_voice_input

def main():
    st.title("Voice Input Test App")
    
    st.write("This is a test application for voice input functionality.")
    
    # Get voice input
    voice_text = get_voice_input()
    
    if voice_text:
        st.write("### Voice Input Received:")
        st.success(voice_text)
        
        # Show what you can do with the text
        st.write("### What you can do with this text:")
        st.code(f"""
# Now you can use this text as an input for your AI model or any other processing
user_query = "{voice_text}"
        """)

if __name__ == "__main__":
    main()